# MemProcFS 网络驱动接口修改指南

## 概述

本指南说明如何将MemProcFS从硬件DMA访问模式修改为网络驱动访问模式。修改后，MemProcFS可以通过网络连接到远程主机，由远程主机的驱动程序读取物理内存，而不是使用本地的FPGA硬件。

## 修改架构

### 原始架构（硬件DMA）
```
MemProcFS → VMM → LeechCore → DeviceFPGA → FPGA硬件 → 目标主机物理内存
```

### 新架构（网络驱动）
```
MemProcFS → VMM → LeechCore → DeviceNetDrv → 网络 → 远程驱动服务 → 目标主机物理内存
```

## 核心修改文件

### 1. leechcore/device_fpga.c
修改现有的FPGA设备实现，添加网络模式支持：
- 在DEVICE_CONTEXT_FPGA结构中添加网络相关字段
- 添加网络通信协议定义和函数
- 实现DeviceFPGA_Network_ReadScatter函数
- 修改DeviceFPGA_Open函数支持网络模式检测
- 根据参数选择硬件FPGA或网络驱动模式

**重要**：不需要修改leechcore.c，保持现有的设备注册逻辑不变

## 使用方法

### 客户端（MemProcFS）

**重要**：设备名称保持为"fpga"，通过参数启用网络模式

#### 方法1：基本用法（默认127.0.0.1:28474）
```c
char* argv[] = { "", "-device", "fpga", "-device-opt", "netdrv=1" };
VMM_HANDLE hVMM = VMMDLL_Initialize(4, argv);
```

#### 方法2：指定IP和端口
```c
char* argv[] = { "", "-device", "fpga", "-device-opt", "netdrv=1,ip=*************,port=28474" };
VMM_HANDLE hVMM = VMMDLL_Initialize(4, argv);
```

#### 方法3：使用network参数
```c
char* argv[] = { "", "-device", "fpga", "-device-opt", "network=1,ip=*************" };
VMM_HANDLE hVMM = VMMDLL_Initialize(4, argv);
```

### 服务端（远程驱动）

在目标主机上运行 `remote_driver_server.exe`，它会：
1. 监听端口28474
2. 接收内存读取请求
3. 通过本地驱动读取物理内存
4. 返回内存数据

## 网络协议

### 包头结构
```c
typedef struct _NETDRV_PACKET_HEADER {
    DWORD dwMagic;          // 魔数：0x4E455444 (请求) / 0x52455350 (响应)
    DWORD dwCommand;        // 命令类型
    DWORD dwDataSize;       // 数据大小
    DWORD dwSequence;       // 序列号
    QWORD qwAddress;        // 物理地址
    DWORD dwLength;         // 读取长度
    DWORD dwFlags;          // 标志位
} NETDRV_PACKET_HEADER;
```

### 支持的命令
- `NETDRV_CMD_READ_MEMORY (0x01)`: 读取物理内存
- `NETDRV_CMD_WRITE_MEMORY (0x02)`: 写入物理内存（可选）
- `NETDRV_CMD_GET_MEMMAP (0x03)`: 获取内存映射（可选）
- `NETDRV_CMD_PING (0x04)`: 连接测试

## 数据流分析

当调用 `VmmScatter_Read` 时，数据流如下：

1. **VMM层**: `VmmReadEx` → `VmmReadScatterVirtual` → `VmmScatter_Read`
2. **LeechCore层**: `LcRead` → `pfnReadScatter`
3. **设备层**: `DeviceFPGA_Network_ReadScatter` → `DeviceFPGA_Network_ReadMemory`
4. **网络层**: 构造请求包 → TCP发送 → 接收响应 → 解析数据
5. **远程驱动**: 接收请求 → 读取物理内存 → 发送响应

## 编译说明

### 修改LeechCore项目
1. 将 `device_netdrv.c` 添加到LeechCore项目
2. 确保链接 `ws2_32.lib`（Windows）或相应的socket库（Linux）
3. 重新编译LeechCore

### 编译示例程序
```bash
# 客户端示例
gcc -o example_netdrv_usage example_netdrv_usage.c -lvmmdll

# 服务端示例
gcc -o remote_driver_server remote_driver_server.c -lws2_32
```

## 部署步骤

### 1. 准备目标主机
- 安装物理内存访问驱动（如WinPmem）
- 编译并运行 `remote_driver_server.exe`
- 确保防火墙允许端口28474

### 2. 配置客户端
- 使用修改后的MemProcFS
- 指定 `netdrv` 设备和目标主机IP

### 3. 测试连接
```c
// 测试基本连接
VMM_HANDLE hVMM = VMMDLL_Initialize(3, argv);
if(hVMM) {
    printf("连接成功!\n");
    VMMDLL_Close(hVMM);
}
```

## 优势

1. **无需硬件**: 不需要FPGA等专用硬件
2. **远程访问**: 可以通过网络访问远程主机内存
3. **灵活部署**: 支持多种网络拓扑
4. **兼容性**: 保持MemProcFS的所有现有功能

## 注意事项

1. **性能**: 网络延迟会影响内存访问速度
2. **安全**: 网络传输的内存数据需要考虑加密
3. **权限**: 远程主机需要管理员权限来访问物理内存
4. **稳定性**: 网络连接中断需要重连机制

## 扩展功能

### 可选实现
1. **加密传输**: 添加TLS/SSL支持
2. **压缩**: 对内存数据进行压缩传输
3. **缓存**: 实现客户端内存缓存
4. **负载均衡**: 支持多个远程驱动服务器
5. **认证**: 添加客户端认证机制

### 高级特性
1. **异步IO**: 实现异步网络操作
2. **批量操作**: 优化多个内存请求的处理
3. **错误恢复**: 自动重连和错误处理
4. **监控**: 添加性能监控和日志记录

## 故障排除

### 常见问题
1. **连接失败**: 检查网络连接和防火墙设置
2. **权限错误**: 确保远程服务以管理员权限运行
3. **内存读取失败**: 检查物理内存驱动是否正确安装
4. **性能问题**: 考虑网络带宽和延迟优化

### 调试技巧
1. 启用详细日志输出
2. 使用网络抓包工具分析协议
3. 检查远程服务器的错误日志
4. 测试不同的内存地址范围

## 总结

通过这个修改方案，您可以成功将MemProcFS从硬件DMA模式转换为网络驱动模式。这为内存分析提供了更大的灵活性和部署选项，特别适合需要远程内存访问的场景。
