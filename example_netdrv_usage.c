// example_netdrv_usage.c : 演示如何使用新的网络驱动接口
//
// 这个示例展示了如何将MemProcFS从硬件DMA模式切换到网络驱动模式
//
#include <stdio.h>
#include <windows.h>
#include "vmmdll.h"

int main()
{
    VMM_HANDLE hVMM = NULL;
    BOOL result;
    BYTE pbMemory[0x1000] = {0};
    DWORD cbRead = 0;
    
    printf("=== MemProcFS 网络驱动模式示例 ===\n\n");
    
    // 原来的硬件DMA方式（FPGA）：
    // char* argv[] = { "", "-device", "fpga" };

    // 新的网络驱动方式（保持设备名为fpga，但启用网络模式）：
    // 方式1：使用默认参数（127.0.0.1:28474）
    char* argv1[] = { "", "-device", "fpga", "-device-opt", "netdrv=1" };

    // 方式2：指定IP和端口
    char* argv2[] = { "", "-device", "fpga", "-device-opt", "netdrv=1,ip=*************,port=28474" };

    // 方式3：使用network参数
    char* argv3[] = { "", "-device", "fpga", "-device-opt", "network=1,ip=*************" };
    
    printf("1. 尝试连接到本地网络驱动 (127.0.0.1:28474)...\n");

    // 初始化MemProcFS，使用网络驱动而不是硬件DMA
    // 注意：设备名仍然是"fpga"，但通过netdrv=1参数启用网络模式
    hVMM = VMMDLL_Initialize(4, argv1);
    if(!hVMM) {
        printf("错误: 无法初始化MemProcFS网络驱动模式\n");
        printf("请确保:\n");
        printf("1. 远程主机上的驱动服务正在运行\n");
        printf("2. 网络连接正常\n");
        printf("3. 防火墙允许连接到端口28474\n");
        return 1;
    }
    
    printf("成功: MemProcFS已连接到网络驱动\n\n");
    
    // 测试内存读取 - 这里的调用路径是：
    // VMMDLL_MemRead -> VmmReadEx -> VmmReadScatterVirtual -> VmmScatter_Read -> 
    // LeechCore -> DeviceNetDrv_ReadScatter -> DeviceNetDrv_ReadMemory -> 网络包
    printf("2. 测试内存读取...\n");
    result = VMMDLL_MemRead(hVMM, -1, 0x1000, pbMemory, 0x1000);
    if(result) {
        printf("成功: 读取了0x1000字节的物理内存\n");
        printf("前16字节: ");
        for(int i = 0; i < 16; i++) {
            printf("%02X ", pbMemory[i]);
        }
        printf("\n\n");
    } else {
        printf("警告: 内存读取失败（可能是地址无效）\n\n");
    }
    
    // 测试scatter读取
    printf("3. 测试scatter读取...\n");
    VMMDLL_SCATTER_HANDLE hS = VMMDLL_Scatter_Initialize(hVMM, -1, VMMDLL_FLAG_NOCACHE);
    if(hS) {
        // 准备多个内存读取请求
        VMMDLL_Scatter_Prepare(hS, 0x1000, 0x1000);
        VMMDLL_Scatter_Prepare(hS, 0x2000, 0x1000);
        VMMDLL_Scatter_Prepare(hS, 0x3000, 0x1000);
        
        // 执行批量读取 - 这会调用到我们的DeviceNetDrv_ReadScatter
        result = VMMDLL_Scatter_Execute(hS);
        if(result) {
            printf("成功: 执行了scatter读取\n");
            
            // 读取结果
            DWORD cbRead1, cbRead2, cbRead3;
            BYTE pbMem1[0x1000], pbMem2[0x1000], pbMem3[0x1000];
            
            VMMDLL_Scatter_Read(hS, 0x1000, 0x1000, pbMem1, &cbRead1);
            VMMDLL_Scatter_Read(hS, 0x2000, 0x1000, pbMem2, &cbRead2);
            VMMDLL_Scatter_Read(hS, 0x3000, 0x1000, pbMem3, &cbRead3);
            
            printf("读取结果: 0x1000=%d字节, 0x2000=%d字节, 0x3000=%d字节\n", 
                cbRead1, cbRead2, cbRead3);
        } else {
            printf("警告: scatter读取失败\n");
        }
        
        VMMDLL_Scatter_CloseHandle(hS);
    }
    
    printf("\n4. 清理资源...\n");
    VMMDLL_Close(hVMM);
    printf("完成!\n\n");
    
    printf("=== 网络协议说明 ===\n");
    printf("当调用VmmScatter_Read时，数据流如下:\n");
    printf("1. VMM层调用LeechCore的LcRead\n");
    printf("2. LeechCore调用DeviceFPGA_Network_ReadScatter（而不是硬件ReadScatter）\n");
    printf("3. 对每个内存块调用DeviceFPGA_Network_ReadMemory\n");
    printf("4. 构造网络包（包含地址、长度等）\n");
    printf("5. 通过TCP发送到远程驱动\n");
    printf("6. 远程驱动读取物理内存并返回数据\n");
    printf("7. 解析响应包并返回内存数据\n\n");
    
    printf("=== 远程驱动实现要点 ===\n");
    printf("远程主机需要实现一个服务，监听端口28474，处理以下命令:\n");
    printf("- NETDRV_CMD_READ_MEMORY (0x01): 读取指定物理地址的内存\n");
    printf("- NETDRV_CMD_WRITE_MEMORY (0x02): 写入内存（可选）\n");
    printf("- NETDRV_CMD_GET_MEMMAP (0x03): 获取内存映射（可选）\n");
    printf("- NETDRV_CMD_PING (0x04): 连接测试\n\n");
    
    return 0;
}
