// remote_driver_server.c : 远程驱动服务器示例
//
// 这个服务器接收来自MemProcFS的网络请求，并通过本地驱动读取物理内存
//
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <windows.h>

#pragma comment(lib, "ws2_32.lib")

// 与device_netdrv.c中相同的协议定义
#define NETDRV_DEFAULT_PORT     28474
#define NETDRV_MAGIC_REQUEST    0x4E455444  // "NETD"
#define NETDRV_MAGIC_RESPONSE   0x52455350  // "RESP"
#define NETDRV_MAX_PACKET_SIZE  0x100000    // 1MB max packet

#pragma pack(push, 1)
typedef struct _NETDRV_PACKET_HEADER {
    DWORD dwMagic;          // 魔数标识
    DWORD dwCommand;        // 命令类型
    DWORD dwDataSize;       // 数据大小
    DWORD dwSequence;       // 序列号
    QWORD qwAddress;        // 物理地址
    DWORD dwLength;         // 读取长度
    DWORD dwFlags;          // 标志位
} NETDRV_PACKET_HEADER, *PNETDRV_PACKET_HEADER;
#pragma pack(pop)

#define NETDRV_CMD_READ_MEMORY      0x01
#define NETDRV_CMD_WRITE_MEMORY     0x02
#define NETDRV_CMD_GET_MEMMAP       0x03
#define NETDRV_CMD_PING             0x04

// 全局变量
HANDLE g_hPhysicalMemory = INVALID_HANDLE_VALUE;

/*
* 初始化物理内存访问
* 这里可以使用各种方法：
* 1. 直接内存访问驱动
* 2. WinPmem驱动
* 3. 其他内存访问方法
*/
BOOL InitializePhysicalMemoryAccess()
{
    // 示例：尝试打开物理内存设备
    // 实际实现中，您需要根据具体的驱动接口来修改这部分
    
    printf("初始化物理内存访问...\n");
    
    // 方法1：尝试打开\\.\PhysicalMemory（需要特殊权限）
    g_hPhysicalMemory = CreateFileA(
        "\\\\.\\PhysicalMemory",
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );
    
    if(g_hPhysicalMemory != INVALID_HANDLE_VALUE) {
        printf("成功: 打开了\\.\PhysicalMemory\n");
        return TRUE;
    }
    
    // 方法2：尝试打开WinPmem驱动
    g_hPhysicalMemory = CreateFileA(
        "\\\\.\\pmem",
        GENERIC_READ | GENERIC_WRITE,
        FILE_SHARE_READ | FILE_SHARE_WRITE,
        NULL,
        OPEN_EXISTING,
        FILE_ATTRIBUTE_NORMAL,
        NULL
    );
    
    if(g_hPhysicalMemory != INVALID_HANDLE_VALUE) {
        printf("成功: 打开了WinPmem驱动\n");
        return TRUE;
    }
    
    printf("警告: 无法打开物理内存设备，将使用模拟数据\n");
    return FALSE;
}

/*
* 读取物理内存
*/
BOOL ReadPhysicalMemory(QWORD qwAddress, DWORD dwLength, PBYTE pbBuffer)
{
    if(g_hPhysicalMemory != INVALID_HANDLE_VALUE) {
        LARGE_INTEGER liOffset;
        DWORD dwBytesRead = 0;
        
        liOffset.QuadPart = qwAddress;
        
        if(SetFilePointerEx(g_hPhysicalMemory, liOffset, NULL, FILE_BEGIN)) {
            if(ReadFile(g_hPhysicalMemory, pbBuffer, dwLength, &dwBytesRead, NULL)) {
                return (dwBytesRead == dwLength);
            }
        }
        return FALSE;
    } else {
        // 模拟数据：填充一些可识别的模式
        for(DWORD i = 0; i < dwLength; i++) {
            pbBuffer[i] = (BYTE)((qwAddress + i) & 0xFF);
        }
        return TRUE;
    }
}

/*
* 处理客户端请求
*/
BOOL HandleClientRequest(SOCKET clientSocket)
{
    NETDRV_PACKET_HEADER reqHeader, respHeader;
    PBYTE pbData = NULL;
    DWORD cbReceived, cbSent;
    BOOL fResult = FALSE;
    
    // 接收请求头
    cbReceived = recv(clientSocket, (char*)&reqHeader, sizeof(reqHeader), MSG_WAITALL);
    if(cbReceived != sizeof(reqHeader)) {
        printf("错误: 接收请求头失败\n");
        return FALSE;
    }
    
    // 验证魔数
    if(reqHeader.dwMagic != NETDRV_MAGIC_REQUEST) {
        printf("错误: 无效的请求魔数: 0x%08x\n", reqHeader.dwMagic);
        return FALSE;
    }
    
    printf("收到请求: 命令=0x%02x, 序列=%d, 地址=0x%016llx, 长度=%d\n",
        reqHeader.dwCommand, reqHeader.dwSequence, reqHeader.qwAddress, reqHeader.dwLength);
    
    // 准备响应头
    respHeader.dwMagic = NETDRV_MAGIC_RESPONSE;
    respHeader.dwCommand = reqHeader.dwCommand;
    respHeader.dwSequence = reqHeader.dwSequence;
    respHeader.qwAddress = reqHeader.qwAddress;
    respHeader.dwLength = reqHeader.dwLength;
    respHeader.dwFlags = 0;
    respHeader.dwDataSize = 0;
    
    switch(reqHeader.dwCommand) {
        case NETDRV_CMD_PING:
            // Ping命令：简单响应
            printf("处理PING命令\n");
            respHeader.dwDataSize = 0;
            fResult = TRUE;
            break;
            
        case NETDRV_CMD_READ_MEMORY:
            // 读取内存命令
            if(reqHeader.dwLength > 0 && reqHeader.dwLength <= NETDRV_MAX_PACKET_SIZE) {
                pbData = malloc(reqHeader.dwLength);
                if(pbData) {
                    if(ReadPhysicalMemory(reqHeader.qwAddress, reqHeader.dwLength, pbData)) {
                        respHeader.dwDataSize = reqHeader.dwLength;
                        fResult = TRUE;
                        printf("成功读取 %d 字节从地址 0x%016llx\n", 
                            reqHeader.dwLength, reqHeader.qwAddress);
                    } else {
                        printf("错误: 读取物理内存失败\n");
                        free(pbData);
                        pbData = NULL;
                    }
                } else {
                    printf("错误: 内存分配失败\n");
                }
            } else {
                printf("错误: 无效的读取长度: %d\n", reqHeader.dwLength);
            }
            break;
            
        case NETDRV_CMD_WRITE_MEMORY:
            // 写入内存命令（可选实现）
            printf("写入内存命令暂未实现\n");
            break;
            
        case NETDRV_CMD_GET_MEMMAP:
            // 获取内存映射命令（可选实现）
            printf("获取内存映射命令暂未实现\n");
            break;
            
        default:
            printf("错误: 未知命令: 0x%02x\n", reqHeader.dwCommand);
            break;
    }
    
    // 发送响应头
    cbSent = send(clientSocket, (char*)&respHeader, sizeof(respHeader), 0);
    if(cbSent != sizeof(respHeader)) {
        printf("错误: 发送响应头失败\n");
        fResult = FALSE;
    }
    
    // 发送数据（如果有）
    if(fResult && pbData && respHeader.dwDataSize > 0) {
        cbSent = send(clientSocket, (char*)pbData, respHeader.dwDataSize, 0);
        if(cbSent != respHeader.dwDataSize) {
            printf("错误: 发送响应数据失败\n");
            fResult = FALSE;
        }
    }
    
    if(pbData) {
        free(pbData);
    }
    
    return fResult;
}

int main()
{
    WSADATA wsaData;
    SOCKET serverSocket, clientSocket;
    struct sockaddr_in serverAddr, clientAddr;
    int clientAddrLen = sizeof(clientAddr);
    
    printf("=== MemProcFS 远程驱动服务器 ===\n\n");
    
    // 初始化Winsock
    if(WSAStartup(MAKEWORD(2,2), &wsaData) != 0) {
        printf("错误: WSAStartup失败\n");
        return 1;
    }
    
    // 初始化物理内存访问
    InitializePhysicalMemoryAccess();
    
    // 创建服务器socket
    serverSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
    if(serverSocket == INVALID_SOCKET) {
        printf("错误: 创建socket失败\n");
        WSACleanup();
        return 1;
    }
    
    // 设置服务器地址
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(NETDRV_DEFAULT_PORT);
    
    // 绑定socket
    if(bind(serverSocket, (struct sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        printf("错误: 绑定端口 %d 失败\n", NETDRV_DEFAULT_PORT);
        closesocket(serverSocket);
        WSACleanup();
        return 1;
    }
    
    // 开始监听
    if(listen(serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        printf("错误: 监听失败\n");
        closesocket(serverSocket);
        WSACleanup();
        return 1;
    }
    
    printf("服务器启动成功，监听端口 %d\n", NETDRV_DEFAULT_PORT);
    printf("等待MemProcFS连接...\n\n");
    
    // 主循环：接受连接并处理请求
    while(TRUE) {
        clientSocket = accept(serverSocket, (struct sockaddr*)&clientAddr, &clientAddrLen);
        if(clientSocket == INVALID_SOCKET) {
            printf("错误: 接受连接失败\n");
            continue;
        }
        
        printf("客户端连接: %s:%d\n", 
            inet_ntoa(clientAddr.sin_addr), ntohs(clientAddr.sin_port));
        
        // 处理客户端请求（简单的单线程处理）
        while(HandleClientRequest(clientSocket)) {
            // 继续处理同一客户端的请求
        }
        
        printf("客户端断开连接\n\n");
        closesocket(clientSocket);
    }
    
    // 清理资源
    closesocket(serverSocket);
    if(g_hPhysicalMemory != INVALID_HANDLE_VALUE) {
        CloseHandle(g_hPhysicalMemory);
    }
    WSACleanup();
    
    return 0;
}
